import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { lazy, Suspense } from "react";
import { Container, Heading, Text } from "@camped-ai/ui";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
import { RoleGuard } from "../../../components/rbac/RoleGuard";
import { Helmet } from "react-helmet"
import sit


// Dynamically import page client for better performance
const BookingsPageClient = lazy(() => import("./page-client"));

const ConciergeBookingsPage = () => {
  return (
    <>
        <Helmet>
      <title>Flinkk Admin</title>
      <link rel="icon" href="/favicon-flinkk.png" />
    </Helmet>
      <PermissionBasedSidebarHider />
      <RoleGuard
        requirePermission="concierge_management:view"
        fallback={
          <Container>
            <div className="p-8 text-center">
              <Heading level="h1">Access Denied</Heading>
              <Text className="mt-2">
                You don't have permission to view bookings.
              </Text>
            </div>
          </Container>
        }
      >
        <Suspense
          fallback={
            <Container className="p-6">
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                <span className="ml-3 text-sm text-gray-600">
                  Loading bookings...
                </span>
              </div>
            </Container>
          }
        >
          <BookingsPageClient />
        </Suspense>
      </RoleGuard>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Concierge Bookings",
});

export default ConciergeBookingsPage;
