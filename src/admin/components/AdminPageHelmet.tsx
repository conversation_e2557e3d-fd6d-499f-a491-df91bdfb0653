import React from "react";
import { siteConfig } from "../../../config/site";
import { Helmet } from "react-helmet";

type Props = {
  suffix?: string;
  titleOverride?: string;
  faviconOverride?: string;
};

/**
 * AdminPageHelmet
 * Renders a consistent document title and favicon for admin pages.
 * Default title: `${siteConfig.title} | Concierge Bookings`
 * Default favicon: `siteConfig.favicon`
 *
 * You can override with:
 * - suffix: changes the text after the site title. Defaults to "Concierge Bookings".
 * - titleOverride: sets the entire title.
 * - faviconOverride: sets a different favicon URL for the page.
 */
const AdminPageHelmet: React.FC<Props> = ({
  suffix = "Concierge Bookings",
  titleOverride,
  faviconOverride,
}) => {
  const title = titleOverride ?? `${siteConfig.title} | ${suffix}`;
  const favicon = faviconOverride ?? siteConfig.favicon;

  return (
    <Helmet>
      <title>{title}</title>
      {favicon ? (
        <link rel="icon" type="image/png" href={favicon} />
      ) : null}
    </Helmet>
  );
};

export default AdminPageHelmet;
